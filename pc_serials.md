{"shipments": [{"handwritten_box_number": "2501", "order_number": "**********", "box_sequence": "012 of 050", "service_tag": "97WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457192", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP012**********6", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 1."}, {"handwritten_box_number": "2502", "order_number": "**********", "box_sequence": "007 of 010", "service_tag": "96WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 10, "total_boxes_in_order": 10, "sscc_id": null, "ship_id": null, "post_code_on_label": null, "cphp_barcode": null, "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": {"carrier": "TNT", "consignment_number": "*********", "piece_sequence": "007 of 010", "service_type": "Economy Express (ND)", "origin_code": "QLZ", "routing_code": "WA1 HNJ", "destination_depot": "BLL", "carrier_barcode_1": "TNTP007**********6", "carrier_barcode_2": "1100*********070875034005700"}, "notes": "Data from page 2."}, {"handwritten_box_number": "2503", "order_number": "**********", "box_sequence": "039 of 050", "service_tag": "87WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457611", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP039**********2", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 6."}, {"handwritten_box_number": "2504", "order_number": "**********", "box_sequence": "041 of 050", "service_tag": "B8WMW34", "customer_po": "**************", "order_date": "09-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457635", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP041**********0", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 5."}, {"handwritten_box_number": "2505", "order_number": "**********", "box_sequence": "023 of 050", "service_tag": "56WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457208", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP023**********4", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 4."}, {"handwritten_box_number": "2506", "order_number": "**********", "box_sequence": "014 of 050", "service_tag": "36WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457383", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP014**********6", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 3."}, {"handwritten_box_number": "2507", "order_number": "**********", "box_sequence": "048 of 050", "service_tag": "C7WMW34", "customer_po": "**************", "order_date": "09-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457246", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP048**********0", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 8."}, {"handwritten_box_number": "2508", "order_number": "**********", "box_sequence": "009 of 050", "service_tag": "B5WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457345", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP009**********8", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 7."}, {"handwritten_box_number": "2509", "order_number": "**********", "box_sequence": "027 of 050", "service_tag": "98WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457505", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP027**********4", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 10. Handwritten number interpreted from 'base'."}, {"handwritten_box_number": "2510", "order_number": "**********", "box_sequence": "008 of 010", "service_tag": "D4WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 10, "total_boxes_in_order": 10, "sscc_id": null, "ship_id": null, "post_code_on_label": null, "cphp_barcode": null, "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": {"carrier": "TNT", "consignment_number": "*********", "piece_sequence": "008 of 010", "service_type": "Economy Express (ND)", "origin_code": "QLZ", "routing_code": "WA1 HNJ", "destination_depot": "BLL", "carrier_barcode_1": "TNTP008**********6", "carrier_barcode_2": "1100*********080875034005700"}, "notes": "Data from page 9."}, {"handwritten_box_number": "2511", "order_number": "**********", "box_sequence": "030 of 050", "service_tag": "D8WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457536", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP030**********2", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 17."}, {"handwritten_box_number": "2512", "order_number": "**********", "box_sequence": "045 of 050", "service_tag": "88WMW34", "customer_po": "**************", "order_date": "09-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457222", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP045**********0", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 13. Handwritten number interpreted from 'else'."}, {"handwritten_box_number": "2513", "order_number": "**********", "box_sequence": "013 of 050", "service_tag": "C5WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457376", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP013**********6", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 16. Corrected OCR error in SSCC ID."}, {"handwritten_box_number": "2514", "order_number": "**********", "box_sequence": "002 of 010", "service_tag": "66WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 10, "total_boxes_in_order": 10, "sscc_id": null, "ship_id": null, "post_code_on_label": null, "cphp_barcode": null, "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": {"carrier": "TNT", "consignment_number": "*********", "piece_sequence": "002 of 010", "service_type": "Economy Express (ND)", "origin_code": "QLZ", "routing_code": "WA1 HNJ", "destination_depot": "BLL", "carrier_barcode_1": "TNTP002**********6", "carrier_barcode_2": "1100*********020875034005700"}, "notes": "Data from page 18."}, {"handwritten_box_number": "2515", "order_number": "**********", "box_sequence": "010 of 010", "service_tag": "J6WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 10, "total_boxes_in_order": 10, "sscc_id": null, "ship_id": null, "post_code_on_label": null, "cphp_barcode": null, "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": {"carrier": "TNT", "consignment_number": "*********", "piece_sequence": "010 of 010", "service_type": "Economy Express (ND)", "origin_code": "QLZ", "routing_code": "WA1 HNJ", "destination_depot": "BLL", "carrier_barcode_1": "TNTP010**********4", "carrier_barcode_2": "1100*********100875034005700"}, "notes": "Data from page 11. Box has a 'Recieved Damaged' sticker."}, {"handwritten_box_number": "2516", "order_number": "**********", "box_sequence": "049 of 050", "service_tag": "18WMW34", "customer_po": "**************", "order_date": "09-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457260", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP049**********0", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 19."}, {"handwritten_box_number": "2517", "order_number": "**********", "box_sequence": "050 of 050", "service_tag": "H9WMW34", "customer_po": "**************", "order_date": "09-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457253", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP050**********8", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 12."}, {"handwritten_box_number": "2518", "order_number": "**********", "box_sequence": "011 of 050", "service_tag": "D6WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457369", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP011**********6", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 15. Handwritten number interpreted from 'Bise'."}, {"handwritten_box_number": "2519", "order_number": "**********", "box_sequence": "031 of 050", "service_tag": "J7WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457543", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP031**********2", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 14. Handwritten number interpreted from 'bise'."}, {"handwritten_box_number": "2522", "order_number": "**********", "box_sequence": "002 of 050", "service_tag": "H6WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457277", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP002**********8", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 20. Handwritten number interpreted from '25023' or '2522'."}, {"handwritten_box_number": "2523", "order_number": "**********", "box_sequence": "040 of 050", "service_tag": "59WMW34", "customer_po": "**************", "order_date": "09-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457628", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP040**********0", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 23. Handwritten number interpreted from 'eese' or '25003'."}, {"handwritten_box_number": "2524", "order_number": "**********", "box_sequence": "005 of 010", "service_tag": "35WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 10, "total_boxes_in_order": 10, "sscc_id": null, "ship_id": null, "post_code_on_label": null, "cphp_barcode": null, "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": {"carrier": "TNT", "consignment_number": "*********", "piece_sequence": "005 of 010", "service_type": "Economy Express (ND)", "origin_code": "QLZ", "routing_code": "WA1 HNJ", "destination_depot": "BLL", "carrier_barcode_1": "TNTP005**********6", "carrier_barcode_2": "1100*********050875034005700"}, "notes": "Data from page 21."}, {"handwritten_box_number": "2525", "order_number": "**********", "box_sequence": "004 of 010", "service_tag": "24WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 10, "total_boxes_in_order": 10, "sscc_id": null, "ship_id": null, "post_code_on_label": null, "cphp_barcode": null, "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": {"carrier": "TNT", "consignment_number": "*********", "piece_sequence": "004 of 010", "service_type": "Economy Express (ND)", "origin_code": "QLZ", "routing_code": "WA1 HNJ", "destination_depot": "BLL", "carrier_barcode_1": "TNTP004**********6", "carrier_barcode_2": "1100*********040875034005700"}, "notes": "Data from page 22. Handwritten number interpreted from 'sese'."}, {"handwritten_box_number": "2526", "order_number": "**********", "box_sequence": "032 of 050", "service_tag": "G8WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457550", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP032**********2", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 24."}, {"handwritten_box_number": "2527", "order_number": "**********", "box_sequence": "003 of 010", "service_tag": "74WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 10, "total_boxes_in_order": 10, "sscc_id": null, "ship_id": null, "post_code_on_label": null, "cphp_barcode": null, "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": {"carrier": "TNT", "consignment_number": "*********", "piece_sequence": "003 of 010", "service_type": "Economy Express (ND)", "origin_code": "QLZ", "routing_code": "WA1 HNJ", "destination_depot": "BLL", "carrier_barcode_1": "TNTP003**********6", "carrier_barcode_2": "1100*********030875034005700"}, "notes": "Data from page 28."}, {"handwritten_box_number": "2528", "order_number": "**********", "box_sequence": "033 of 050", "service_tag": "27WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457567", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP033**********2", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from pages 27 & 30. Handwritten number interpreted from 'sese'."}, {"handwritten_box_number": "2529", "order_number": "**********", "box_sequence": "034 of 050", "service_tag": "77WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457215", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP034**********2", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 26. Handwritten number interpreted from 'bese'."}, {"handwritten_box_number": "2530", "order_number": "**********", "box_sequence": "043 of 050", "service_tag": "D7WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457659", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP043**********0", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 25."}, {"handwritten_box_number": "2531", "order_number": "**********", "box_sequence": "005 of 050", "service_tag": "F5WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457307", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP005**********8", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 29."}, {"handwritten_box_number": "2532", "order_number": "**********", "box_sequence": "010 of 050", "service_tag": "B7WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457352", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP010**********6", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 31."}, {"handwritten_box_number": "2533", "order_number": "**********", "box_sequence": "008 of 050", "service_tag": "55WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457338", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP008**********8", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 35."}, {"handwritten_box_number": "2534", "order_number": "**********", "box_sequence": "019 of 050", "service_tag": "46WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457444", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP019**********6", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 34."}, {"handwritten_box_number": "2535", "order_number": "**********", "box_sequence": "036 of 050", "service_tag": "47WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457581", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP036**********2", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from pages 32 & 33."}, {"handwritten_box_number": "2536", "order_number": "**********", "box_sequence": "018 of 050", "service_tag": "H5WMW34", "customer_po": "**************", "order_date": "09-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457420", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP018**********6", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 36."}, {"handwritten_box_number": "2537", "order_number": "**********", "box_sequence": "026 of 050", "service_tag": "B6WMW34", "customer_po": "**************", "order_date": "09-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457499", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP026**********4", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 37. Handwritten number interpreted from 'tese'."}, {"handwritten_box_number": "2538", "order_number": "**********", "box_sequence": "042 of 050", "service_tag": "89WMW34", "customer_po": "**************", "order_date": "09-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457642", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP042**********0", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 38."}, {"handwritten_box_number": "2539", "order_number": "**********", "box_sequence": "009 of 010", "service_tag": "15WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 10, "total_boxes_in_order": 10, "sscc_id": null, "ship_id": null, "post_code_on_label": null, "cphp_barcode": null, "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": {"carrier": "TNT", "consignment_number": "*********", "piece_sequence": "009 of 010", "service_type": "Economy Express (ND)", "origin_code": "QLZ", "routing_code": "WA1 HNJ", "destination_depot": "BLL", "carrier_barcode_1": "TNTP009**********6", "carrier_barcode_2": "1100*********090875034005700"}, "notes": "Data from page 39."}, {"handwritten_box_number": "2540", "order_number": "**********", "box_sequence": "038 of 050", "service_tag": "17WMW34", "customer_po": "**************", "order_date": "09-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457604", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP038**********2", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 41. Handwritten number interpreted from '<PERSON><PERSON>'."}, {"handwritten_box_number": "2541", "order_number": "**********", "box_sequence": "004 of 050", "service_tag": "95WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457291", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP004**********8", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 40."}, {"handwritten_box_number": "2542", "order_number": "**********", "box_sequence": "021 of 050", "service_tag": "67WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457451", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP021**********4", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 46."}, {"handwritten_box_number": "2543", "order_number": "**********", "box_sequence": "044 of 050", "service_tag": "28WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457666", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP044**********0", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 45."}, {"handwritten_box_number": "2544", "order_number": "**********", "box_sequence": "035 of 050", "service_tag": "G7WMW34", "customer_po": "**************", "order_date": "09-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457574", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP035**********2", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 44. Handwritten number interpreted from '44 se'."}, {"handwritten_box_number": "2545", "order_number": "**********", "box_sequence": "047 of 050", "service_tag": "F9WMW34", "customer_po": "**************", "order_date": "09-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457239", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP047**********0", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 43. Handwritten number interpreted from '<PERSON><PERSON>'."}, {"handwritten_box_number": "2546", "order_number": "**********", "box_sequence": "001 of 010", "service_tag": "SERVICE_TAG_UNREADABLE", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 10, "total_boxes_in_order": 10, "sscc_id": null, "ship_id": null, "post_code_on_label": null, "cphp_barcode": null, "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": {"carrier": "TNT", "consignment_number": "*********", "piece_sequence": "001 of 010", "service_type": "Economy Express (ND)", "origin_code": "QLZ", "routing_code": "WA1 HNJ", "destination_depot": "BLL", "carrier_barcode_1": "TNTP001**********6", "carrier_barcode_2": "1100*********010875034005700"}, "notes": "Data from page 42. Service Tag was unreadable due to poor OCR quality ('SOWMWS!')."}, {"handwritten_box_number": "2547", "order_number": "**********", "box_sequence": "022 of 050", "service_tag": "48WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457468", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP022**********4", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 47. Corrected OCR error in SSCC ID."}, {"handwritten_box_number": "2548", "order_number": "**********", "box_sequence": "025 of 050", "service_tag": "57WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457482", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP025**********4", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 48. Corrected OCR error in SSCC ID."}, {"handwritten_box_number": "2549", "order_number": "**********", "box_sequence": "037 of 050", "service_tag": "39WMW34", "customer_po": "**************", "order_date": "09-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457598", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP037**********2", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 49."}, {"handwritten_box_number": "2550", "order_number": "**********", "box_sequence": "046 of 050", "service_tag": "F7WMW34", "customer_po": "**************", "order_date": "09-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457673", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP046**********0", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 50."}, {"handwritten_box_number": "2551", "order_number": "**********", "box_sequence": "006 of 050", "service_tag": "75WMW34", "customer_po": "**************", "order_date": "08-10-2024", "model_number": "HM2CF", "weight_kg": 12, "total_systems_in_order": 50, "total_boxes_in_order": 50, "sscc_id": "(00)505397241286457314", "ship_id": "PLAN.**********", "post_code_on_label": "(421)2085700", "cphp_barcode": "CPHP006**********8", "shipping_address": {"attention": "Accounts Payable", "company": "Danoffice IT ApS", "street": "Englandsvej 14", "city": "Svendborg", "postal_code": "5700", "country_code": "DK"}, "carrier_details": null, "notes": "Data from page 51. Handwritten number is partially cut off but inferred as 2551."}]}