<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shipping Details</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
        }
        th {
            background-color: #f2f2f2;
            text-align: left;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <h1>Shipping Details</h1>
    <button onclick="copyTableData()">Copy Table Data</button>
    <table id="shippingTable">
        <thead>
            <tr>
                <th>Handwritten Box Number</th>
                <th>Service Tag</th>
            </tr>
        </thead>
        <tbody>
            <!-- Data rows will be inserted here -->
            <tr>
                <td>1</td>
                <td>ABC123</td>
            </tr>
            <tr>
                <td>2</td>
                <td>DEF456</td>
            </tr>
            <tr>
                <td>3</td>
                <td>GHI789</td>
            </tr>
            <!-- Add more rows as needed -->
        </tbody>
    </table>

    <script>
        function copyTableData() {
            const table = document.getElementById('shippingTable');
            let tableData = '';

            // Get table headers
            const headers = table.querySelectorAll('th');
            headers.forEach(header => {
                tableData += header.innerText + '\t';
            });
            tableData += '\n';

            // Get table rows
            const rows = table.querySelectorAll('tr');
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                cells.forEach(cell => {
                    tableData += cell.innerText + '\t';
                });
                tableData += '\n';
            });

            // Copy to clipboard
            const tempInput = document.createElement('textarea');
            tempInput.value = tableData;
            document.body.appendChild(tempInput);
            tempInput.select();
            document.execCommand('copy');
            document.body.removeChild(tempInput);

            alert('Table data copied to clipboard!');
        }
    </script>
</body>
</html>