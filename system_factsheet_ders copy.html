<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DERS - Comprehensive Recruitment System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header .subtitle {
            font-size: 1.3rem;
            opacity: 0.9;
            margin-bottom: 5px;
        }

        .header .location {
            font-size: 1.1rem;
            opacity: 0.8;
            background: rgba(255,255,255,0.2);
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            margin-top: 10px;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .section:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }

        .section-title {
            font-size: 2rem;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .section-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 25px;
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .card ul {
            list-style: none;
            padding: 0;
        }

        .card li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(0,0,0,0.1);
            position: relative;
            padding-left: 20px;
        }

        .card li:before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #667eea;
            font-weight: bold;
        }

        .workflow {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .workflow-step {
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            position: relative;
            transition: all 0.3s ease;
        }

        .workflow-step:hover {
            transform: scale(1.02);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .step-number {
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
            margin-right: 20px;
            flex-shrink: 0;
        }

        .step-content h4 {
            font-size: 1.3rem;
            margin-bottom: 10px;
        }

        .step-content p {
            opacity: 0.9;
            line-height: 1.5;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-card {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px) scale(1.05);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
        }

        .tech-item {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .tech-item:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .roles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .role-card {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .role-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: all 0.6s;
            transform: rotate(45deg);
            opacity: 0;
        }

        .role-card:hover::before {
            animation: shine 0.6s ease-in-out;
        }

        .role-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .role-title {
            font-size: 1.4rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .role-level {
            background: rgba(255,255,255,0.5);
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 0.9rem;
            color: #2c3e50;
            display: inline-block;
            margin-bottom: 15px;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .benefit-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .benefit-card:hover {
            transform: translateY(-5px) rotate(2deg);
        }

        .benefit-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            display: block;
        }

        .benefit-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .benefit-desc {
            color: #5a6c7d;
            line-height: 1.5;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes shine {
            0% {
                opacity: 0;
                transform: rotate(45deg) translate(-100%, -100%);
            }
            50% {
                opacity: 1;
            }
            100% {
                opacity: 0;
                transform: rotate(45deg) translate(100%, 100%);
            }
        }

        .footer {
            text-align: center;
            color: white;
            padding: 30px;
            margin-top: 40px;
            background: rgba(0,0,0,0.2);
            border-radius: 15px;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }
            
            .container {
                padding: 15px;
            }
            
            .section {
                padding: 20px;
            }
            
            .workflow-step {
                flex-direction: column;
                text-align: center;
            }
            
            .step-number {
                margin-right: 0;
                margin-bottom: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>DERS</h1>
            <div class="subtitle">Dakoii Echad Recruitment & Selection System</div>
            <div class="location">🏝️ Papua New Guinea Government</div>
        </div>

        <!-- System Overview -->
        <div class="section">
            <h2 class="section-title">
                <div class="section-icon">🎯</div>
                System Overview
            </h2>
            <p style="font-size: 1.2rem; line-height: 1.6; color: #5a6c7d; text-align: center; margin-bottom: 30px;">
                A comprehensive web-based platform designed to streamline and digitize government recruitment processes, 
                ensuring compliance with Public Service General Orders while providing an integrated solution for the entire recruitment lifecycle.
            </p>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">70%</div>
                    <div class="stat-label">Time Reduction</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">1000+</div>
                    <div class="stat-label">Concurrent Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">99.5%</div>
                    <div class="stat-label">Uptime Target</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">10K+</div>
                    <div class="stat-label">Applications/Exercise</div>
                </div>
            </div>
        </div>

        <!-- Technology Stack -->
        <div class="section">
            <h2 class="section-title">
                <div class="section-icon">⚙️</div>
                Technology Stack
            </h2>
            <div class="tech-stack">
                <div class="tech-item">CodeIgniter 4</div>
                <div class="tech-item">PHP 8.1+</div>
                <div class="tech-item">MySQL/MariaDB</div>
                <div class="tech-item">Bootstrap</div>
                <div class="tech-item">Apache/Nginx</div>
                <div class="tech-item">Session Auth</div>
                <div class="tech-item">File Management</div>
            </div>
        </div>

        <!-- User Roles -->
        <div class="section">
            <h2 class="section-title">
                <div class="section-icon">👥</div>
                User Roles & Access Levels
            </h2>
            <div class="roles-grid">
                <div class="role-card">
                    <div class="role-title">System Administrator</div>
                    <div class="role-level">Highest Privileges</div>
                    <ul>
                        <li>Organization management</li>
                        <li>System user creation</li>
                        <li>Geographic data management</li>
                        <li>System-wide configuration</li>
                    </ul>
                </div>
                <div class="role-card">
                    <div class="role-title">Organization Admin</div>
                    <div class="role-level">Org-Specific Rights</div>
                    <ul>
                        <li>User management</li>
                        <li>Exercise creation</li>
                        <li>Position definition</li>
                        <li>Application oversight</li>
                    </ul>
                </div>
                <div class="role-card">
                    <div class="role-title">HR Supervisor</div>
                    <div class="role-level">Departmental Supervision</div>
                    <ul>
                        <li>Application review</li>
                        <li>Interview coordination</li>
                        <li>Selection recommendations</li>
                        <li>Quality assurance</li>
                    </ul>
                </div>
                <div class="role-card">
                    <div class="role-title">HR User/Officer</div>
                    <div class="role-level">Operational Tasks</div>
                    <ul>
                        <li>Application pre-screening</li>
                        <li>Applicant profiling</li>
                        <li>Interview scheduling</li>
                        <li>Document verification</li>
                    </ul>
                </div>
                <div class="role-card">
                    <div class="role-title">Applicants</div>
                    <div class="role-level">Self-Service Portal</div>
                    <ul>
                        <li>Profile management</li>
                        <li>Job applications</li>
                        <li>Status tracking</li>
                        <li>Document upload</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Recruitment Workflow -->
        <div class="section">
            <h2 class="section-title">
                <div class="section-icon">🔄</div>
                Recruitment Process Workflow
            </h2>
            <div class="workflow">
                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h4>Pre-Application Setup</h4>
                        <p>Exercise creation, position definition, publication, and application period setup</p>
                    </div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h4>Application Collection</h4>
                        <p>Applicant registration, document upload, validation, and receipt confirmation</p>
                    </div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h4>Pre-Screening Process</h4>
                        <p>Initial review, document verification, eligibility assessment, and status assignment</p>
                    </div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h4>Profiling & Rating</h4>
                        <p>Detailed profiling, multi-criteria scoring, AI-assisted rating, and manual review</p>
                    </div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <h4>Shortlisting</h4>
                        <p>Ranking generation, shortlist creation, candidate notification, and interview scheduling</p>
                    </div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">6</div>
                    <div class="step-content">
                        <h4>Interview Management</h4>
                        <p>Panel setup, question bank, scoring framework, session management, and final assessment</p>
                    </div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">7</div>
                    <div class="step-content">
                        <h4>Final Selection</h4>
                        <p>Merit ranking, selection recommendations, approval workflow, and offer generation</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Core Features -->
        <div class="section">
            <h2 class="section-title">
                <div class="section-icon">🚀</div>
                Core System Features
            </h2>
            <div class="grid">
                <div class="card">
                    <h3>📊 Organization Management</h3>
                    <ul>
                        <li>Multi-tenant architecture</li>
                        <li>Organization licensing control</li>
                        <li>Location-based access</li>
                        <li>Customizable branding</li>
                    </ul>
                </div>
                <div class="card">
                    <h3>📋 Exercise Management</h3>
                    <ul>
                        <li>Comprehensive exercise creation</li>
                        <li>Advertisement integration</li>
                        <li>Multi-status workflow</li>
                        <li>Timeline management</li>
                    </ul>
                </div>
                <div class="card">
                    <h3>💼 Position Management</h3>
                    <ul>
                        <li>Detailed job descriptions</li>
                        <li>Qualification requirements</li>
                        <li>Salary range setup</li>
                        <li>Skills mapping</li>
                    </ul>
                </div>
                <div class="card">
                    <h3>👤 Applicant Portal</h3>
                    <ul>
                        <li>User-friendly registration</li>
                        <li>Comprehensive profiling</li>
                        <li>Document management</li>
                        <li>One-click applications</li>
                    </ul>
                </div>
                <div class="card">
                    <h3>📈 Reporting Suite</h3>
                    <ul>
                        <li>Application analytics</li>
                        <li>Pre-screening reports</li>
                        <li>Scoring distributions</li>
                        <li>Selection tracking</li>
                    </ul>
                </div>
                <div class="card">
                    <h3>🔒 Security & Compliance</h3>
                    <ul>
                        <li>Role-based access control</li>
                        <li>Data encryption</li>
                        <li>Audit trails</li>
                        <li>Regulatory compliance</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- System Benefits -->
        <div class="section">
            <h2 class="section-title">
                <div class="section-icon">✨</div>
                System Benefits & Impact
            </h2>
            <div class="benefits-grid">
                <div class="benefit-card">
                    <div class="benefit-icon">🏢</div>
                    <div class="benefit-title">For Organizations</div>
                    <div class="benefit-desc">70% reduction in processing time, standardized processes, automated compliance, and significant cost savings</div>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">👨‍💼</div>
                    <div class="benefit-title">For Applicants</div>
                    <div class="benefit-desc">24/7 accessibility, real-time tracking, single platform convenience, and fair standardized processes</div>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">🏛️</div>
                    <div class="benefit-title">For Government</div>
                    <div class="benefit-desc">Improved quality, enhanced accountability, streamlined efficiency, and data-driven insights</div>
                </div>
            </div>
        </div>

        <!-- Future Roadmap -->
        <div class="section">
            <h2 class="section-title">
                <div class="section-icon">🔮</div>
                Future Enhancements
            </h2>
            <div class="grid">
                <div class="card">
                    <h3>📱 Phase 2 Developments</h3>
                    <ul>
                        <li>Native mobile applications</li>
                        <li>RESTful API integration</li>
                        <li>Machine learning analytics</li>
                        <li>Blockchain record keeping</li>
                        <li>Multi-language support</li>
                    </ul>
                </div>
                <div class="card">
                    <h3>🔗 Integration Opportunities</h3>
                    <ul>
                        <li>HRMIS system connection</li>
                        <li>Payment gateway integration</li>
                        <li>Video interviewing platform</li>
                        <li>Social media integration</li>
                        <li>Government portal connection</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <h3>🌟 DERS - Leading Government Recruitment Innovation</h3>
            <p>Transforming traditional paper-based recruitment into streamlined digital workflows</p>
            <p><strong>Version:</strong> v1.0 | <strong>Framework:</strong> CodeIgniter 4 | <strong>Country:</strong> Papua New Guinea</p>
        </div>
    </div>
</body>
</html>