<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DERS - Comprehensive Recruitment System</title>
    <!-- Added external JavaScript libraries for infographics -->
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/countup.js/2.0.8/countUp.min.js"></script>
    <!-- Added Font Awesome for better icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Added Animate.css for enhanced animations -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #000000;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
            background: #e60000;
            padding: 50px 20px;
            position: relative;
        }
        
        .header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 20px;
            background: #ffc107;
            clip-path: polygon(0 0, 100% 0, 100% 100%, 0 0);
        }

        .header h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header .subtitle {
            font-size: 1.3rem;
            opacity: 0.9;
            margin-bottom: 5px;
        }

        .header .location {
            font-size: 1.1rem;
            opacity: 0.8;
            background: rgba(255,255,255,0.2);
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            margin-top: 10px;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .section:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }

        .section-title {
            font-size: 2rem;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .section-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #e60000, #cc0000);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 25px;
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            height: auto; /* Allow height to adjust to content */
            min-height: 100px; /* Set minimum height */
            display: flex;
            flex-direction: column;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, #e60000, #cc0000);
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .card ul {
            list-style: none;
            padding: 0;
        }

        .card li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(0,0,0,0.1);
            position: relative;
            padding-left: 20px;
        }

        .card li:before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #e60000;
            font-weight: bold;
        }

        .workflow {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .workflow-step {
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, #e60000 0%, #cc0000 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            position: relative;
            transition: all 0.3s ease;
        }

        .workflow-step:hover {
            transform: scale(1.02);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .step-number {
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
            margin-right: 20px;
            flex-shrink: 0;
        }

        .step-content h4 {
            font-size: 1.3rem;
            margin-bottom: 10px;
        }

        .step-content p {
            opacity: 0.9;
            line-height: 1.5;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-card {
            background: linear-gradient(135deg, #e60000 0%, #cc0000 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
            height: auto; /* Allow height to adjust to content */
            min-height: 150px; /* Set minimum height */
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .stat-card:hover {
            transform: translateY(-5px) scale(1.05);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
        }

        .tech-item {
            background: linear-gradient(135deg, #e60000, #cc0000);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .tech-item:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .roles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .role-card {
            background: linear-gradient(135deg, #fff8e1 0%, #ffc107 100%);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            height: auto; /* Allow height to adjust to content */
            min-height: 150px; /* Set minimum height */
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .role-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: all 0.6s;
            transform: rotate(45deg);
            opacity: 0;
        }

        .role-card:hover::before {
            animation: shine 0.6s ease-in-out;
        }

        .role-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .role-title {
            font-size: 1.4rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .role-level {
            background: rgba(255,255,255,0.5);
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 0.9rem;
            color: #2c3e50;
            display: inline-block;
            margin-bottom: 15px;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .benefit-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            height: auto; /* Allow height to adjust to content */
            min-height: 150px; /* Set minimum height */
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .benefit-card:hover {
            transform: translateY(-5px) rotate(2deg);
        }

        .benefit-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            display: block;
        }

        .benefit-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .benefit-desc {
            color: #5a6c7d;
            line-height: 1.5;
        }

        /* Added styles for new infographic elements */
        .chart-container {
            width: 100%;
            height: 300px;
            margin: 20px 0;
            position: relative;
        }

        .donut-chart-container {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            margin: 30px 0;
        }

        .donut-chart {
            width: 200px;
            height: 200px;
            position: relative;
            margin: 15px;
        }

        .donut-label {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            font-weight: bold;
            color: #2c3e50;
        }

        .timeline {
            position: relative;
            max-width: 1200px;
            margin: 30px auto;
        }

        .timeline::after {
            content: '';
            position: absolute;
            width: 6px;
            background: linear-gradient(to bottom, #e60000, #cc0000);
            top: 0;
            bottom: 0;
            left: 50%;
            margin-left: -3px;
            border-radius: 10px;
        }

        .timeline-container {
            padding: 10px 40px;
            position: relative;
            width: 50%;
            box-sizing: border-box;
        }

        .timeline-container::after {
            content: '';
            position: absolute;
            width: 25px;
            height: 25px;
            background: linear-gradient(135deg, #e60000, #cc0000);
            border-radius: 50%;
            top: 15px;
            z-index: 1;
        }

        .left {
            left: 0;
        }

        .right {
            left: 50%;
        }

        .left::after {
            right: -12px;
        }

        .right::after {
            left: -12px;
        }

        .timeline-content {
            padding: 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .progress-container {
            width: 100%;
            background-color: #e9ecef;
            border-radius: 10px;
            margin: 15px 0;
            overflow: hidden;
        }

        .progress-bar {
            height: 20px;
            border-radius: 10px;
            background: linear-gradient(135deg, #e60000, #cc0000);
            transition: width 1.5s ease-in-out;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 10px;
            color: white;
            font-weight: bold;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes shine {
            0% {
                opacity: 0;
                transform: rotate(45deg) translate(-100%, -100%);
            }
            50% {
                opacity: 1;
            }
            100% {
                opacity: 0;
                transform: rotate(45deg) translate(100%, 100%);
            }
        }

        .footer {
            text-align: center;
            color: white;
            padding: 30px;
            margin-top: 40px;
            background: #000000;
            border-radius: 15px;
            border-bottom: 5px solid #ffc107;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }
            
            .container {
                padding: 15px;
            }
            
            .section {
                padding: 20px;
            }
            
            .workflow-step {
                flex-direction: column;
                text-align: center;
            }
            
            .step-number {
                margin-right: 0;
                margin-bottom: 15px;
            }

            /* Responsive adjustments for infographics */
            .timeline::after {
                left: 31px;
            }
            
            .timeline-container {
                width: 100%;
                padding-left: 70px;
                padding-right: 25px;
            }
            
            .timeline-container::after {
                left: 15px;
            }
            
            .right {
                left: 0%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>DERS</h1>
            <div class="subtitle">Dakoii Echad Recruitment & Selection System</div>
            <div class="location">🏝️ Papua New Guinea Government</div>
        </div>

        <!-- System Overview -->
        <div class="section">
            <h2 class="section-title">
                <div class="section-icon"><i class="fas fa-bullseye"></i></div>
                System Overview
            </h2>
            <p style="font-size: 1.2rem; line-height: 1.6; color: #5a6c7d; text-align: center; margin-bottom: 30px;">
                A comprehensive web-based platform designed to streamline and digitize government recruitment processes, 
                ensuring compliance with Public Service General Orders while providing an integrated solution for the entire recruitment lifecycle.
            </p>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="time-reduction">70%</div>
                    <div class="stat-label">Time Reduction</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="concurrent-users">1000+</div>
                    <div class="stat-label">Concurrent Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="uptime">99.5%</div>
                    <div class="stat-label">Uptime Target</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="applications">10K+</div>
                    <div class="stat-label">Applications/Exercise</div>
                </div>
            </div>

            <!-- Added Performance Metrics Chart -->
            <div class="chart-container">
                <canvas id="performanceChart"></canvas>
            </div>
        </div>

        <!-- Technology Stack -->
        <div class="section">
            <h2 class="section-title">
                <div class="section-icon"><i class="fas fa-cogs"></i></div>
                Technology Stack
            </h2>
            <div class="tech-stack">
                <div class="tech-item"><i class="fab fa-php"></i> PHP 8.1+</div>
                <div class="tech-item"><i class="fas fa-code"></i> CodeIgniter 4</div>
                <div class="tech-item"><i class="fas fa-database"></i> MySQL/MariaDB</div>
                <div class="tech-item"><i class="fab fa-bootstrap"></i> Bootstrap</div>
                <div class="tech-item"><i class="fas fa-server"></i> Apache/Nginx</div>
                <div class="tech-item"><i class="fas fa-lock"></i> Session Auth</div>
                <div class="tech-item"><i class="fas fa-file"></i> File Management</div>
            </div>

            <!-- Added Technology Usage Donut Charts -->
            <div class="donut-chart-container">
                <div class="donut-chart">
                    <canvas id="backendChart"></canvas>
                    <div class="donut-label">Backend</div>
                </div>
                <div class="donut-chart">
                    <canvas id="frontendChart"></canvas>
                    <div class="donut-label">Frontend</div>
                </div>
                <div class="donut-chart">
                    <canvas id="infrastructureChart"></canvas>
                    <div class="donut-label">Infrastructure</div>
                </div>
            </div>
        </div>

        <!-- User Roles -->
        <div class="section">
            <h2 class="section-title">
                <div class="section-icon"><i class="fas fa-users"></i></div>
                User Roles & Access Levels
            </h2>
            <div class="roles-grid">
                <div class="role-card">
                    <div class="role-title">System Administrator</div>
                    <div class="role-level">Highest Privileges</div>
                    <ul>
                        <li>Organization management</li>
                        <li>System user creation</li>
                        <li>Geographic data management</li>
                        <li>System-wide configuration</li>
                    </ul>
                </div>
                <div class="role-card">
                    <div class="role-title">Organization Admin</div>
                    <div class="role-level">Org-Specific Rights</div>
                    <ul>
                        <li>User management</li>
                        <li>Exercise creation</li>
                        <li>Position definition</li>
                        <li>Application oversight</li>
                    </ul>
                </div>
                <div class="role-card">
                    <div class="role-title">HR Supervisor</div>
                    <div class="role-level">Departmental Supervision</div>
                    <ul>
                        <li>Application review</li>
                        <li>Interview coordination</li>
                        <li>Selection recommendations</li>
                        <li>Quality assurance</li>
                    </ul>
                </div>
                <div class="role-card">
                    <div class="role-title">HR User/Officer</div>
                    <div class="role-level">Operational Tasks</div>
                    <ul>
                        <li>Application pre-screening</li>
                        <li>Applicant profiling</li>
                        <li>Interview scheduling</li>
                        <li>Document verification</li>
                    </ul>
                </div>
                <div class="role-card">
                    <div class="role-title">Applicants</div>
                    <div class="role-level">Self-Service Portal</div>
                    <ul>
                        <li>Profile management</li>
                        <li>Job applications</li>
                        <li>Status tracking</li>
                        <li>Document upload</li>
                    </ul>
                </div>
            </div>

            <!-- Added User Role Distribution Chart -->
            <div class="chart-container">
                <canvas id="roleDistributionChart"></canvas>
            </div>
        </div>

        <!-- Recruitment Workflow -->
        <div class="section">
            <h2 class="section-title">
                <div class="section-icon"><i class="fas fa-sync-alt"></i></div>
                Recruitment Process Workflow
            </h2>
            <div class="workflow">
                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h4>Pre-Application Setup</h4>
                        <p>Exercise creation, position definition, publication, and application period setup</p>
                    </div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h4>Application Collection</h4>
                        <p>Applicant registration, document upload, validation, and receipt confirmation</p>
                    </div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h4>Pre-Screening Process</h4>
                        <p>Initial review, document verification, eligibility assessment, and status assignment</p>
                    </div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h4>Profiling & Rating</h4>
                        <p>Detailed profiling, multi-criteria scoring, AI-assisted rating, and manual review</p>
                    </div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <h4>Shortlisting</h4>
                        <p>Ranking generation, shortlist creation, candidate notification, and interview scheduling</p>
                    </div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">6</div>
                    <div class="step-content">
                        <h4>Interview Management</h4>
                        <p>Panel setup, question bank, scoring framework, session management, and final assessment</p>
                    </div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">7</div>
                    <div class="step-content">
                        <h4>Final Selection</h4>
                        <p>Merit ranking, selection recommendations, approval workflow, and offer generation</p>
                    </div>
                </div>
            </div>

            <!-- Added Interactive Timeline -->
            <h3 style="text-align: center; margin: 40px 0 20px; color: #2c3e50;">Typical Recruitment Timeline</h3>
            <div class="timeline">
                <div class="timeline-container left">
                    <div class="timeline-content">
                        <h3>Week 1-2</h3>
                        <p>Position definition and advertisement preparation</p>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: 0%" data-width="100%">100%</div>
                        </div>
                    </div>
                </div>
                <div class="timeline-container right">
                    <div class="timeline-content">
                        <h3>Week 3-6</h3>
                        <p>Application collection period</p>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: 0%" data-width="100%">100%</div>
                        </div>
                    </div>
                </div>
                <div class="timeline-container left">
                    <div class="timeline-content">
                        <h3>Week 7-8</h3>
                        <p>Pre-screening and initial assessment</p>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: 0%" data-width="85%">85%</div>
                        </div>
                    </div>
                </div>
                <div class="timeline-container right">
                    <div class="timeline-content">
                        <h3>Week 9-10</h3>
                        <p>Shortlisting and interview preparation</p>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: 0%" data-width="70%">70%</div>
                        </div>
                    </div>
                </div>
                <div class="timeline-container left">
                    <div class="timeline-content">
                        <h3>Week 11-12</h3>
                        <p>Interviews and final selection</p>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: 0%" data-width="60%">60%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Core Features -->
        <div class="section">
            <h2 class="section-title">
                <div class="section-icon"><i class="fas fa-rocket"></i></div>
                Core System Features
            </h2>
            <div class="grid">
                <div class="card">
                    <h3><i class="fas fa-chart-pie"></i> Organization Management</h3>
                    <ul>
                        <li>Multi-tenant architecture</li>
                        <li>Organization licensing control</li>
                        <li>Location-based access</li>
                        <li>Customizable branding</li>
                    </ul>
                </div>
                <div class="card">
                    <h3><i class="fas fa-clipboard-list"></i> Exercise Management</h3>
                    <ul>
                        <li>Comprehensive exercise creation</li>
                        <li>Advertisement integration</li>
                        <li>Multi-status workflow</li>
                        <li>Timeline management</li>
                    </ul>
                </div>
                <div class="card">
                    <h3><i class="fas fa-briefcase"></i> Position Management</h3>
                    <ul>
                        <li>Detailed job descriptions</li>
                        <li>Qualification requirements</li>
                        <li>Salary range setup</li>
                        <li>Skills mapping</li>
                    </ul>
                </div>
                <div class="card">
                    <h3><i class="fas fa-user"></i> Applicant Portal</h3>
                    <ul>
                        <li>User-friendly registration</li>
                        <li>Comprehensive profiling</li>
                        <li>Document management</li>
                        <li>One-click applications</li>
                    </ul>
                </div>
                <div class="card">
                    <h3><i class="fas fa-chart-line"></i> Reporting Suite</h3>
                    <ul>
                        <li>Application analytics</li>
                        <li>Pre-screening reports</li>
                        <li>Scoring distributions</li>
                        <li>Selection tracking</li>
                    </ul>
                </div>
                <div class="card">
                    <h3><i class="fas fa-shield-alt"></i> Security & Compliance</h3>
                    <ul>
                        <li>Role-based access control</li>
                        <li>Data encryption</li>
                        <li>Audit trails</li>
                        <li>Regulatory compliance</li>
                    </ul>
                </div>
            </div>

            <!-- Added Feature Usage Radar Chart -->
            <div class="chart-container">
                <canvas id="featureUsageChart"></canvas>
            </div>
        </div>

        <!-- System Benefits -->
        <div class="section">
            <h2 class="section-title">
                <div class="section-icon"><i class="fas fa-star"></i></div>
                System Benefits & Impact
            </h2>
            <div class="benefits-grid">
                <div class="benefit-card">
                    <div class="benefit-icon"><i class="fas fa-building"></i></div>
                    <div class="benefit-title">For Organizations</div>
                    <div class="benefit-desc">70% reduction in processing time, standardized processes, automated compliance, and significant cost savings</div>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon"><i class="fas fa-user-tie"></i></div>
                    <div class="benefit-title">For Applicants</div>
                    <div class="benefit-desc">24/7 accessibility, real-time tracking, single platform convenience, and fair standardized processes</div>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon"><i class="fas fa-landmark"></i></div>
                    <div class="benefit-title">For Government</div>
                    <div class="benefit-desc">Improved quality, enhanced accountability, streamlined efficiency, and data-driven insights</div>
                </div>
            </div>

            <!-- Added Benefits Comparison Chart -->
            <div class="chart-container">
                <canvas id="benefitsComparisonChart"></canvas>
            </div>
        </div>

        <!-- Future Roadmap -->
        <div class="section">
            <h2 class="section-title">
                <div class="section-icon"><i class="fas fa-crystal-ball"></i></div>
                Future Enhancements
            </h2>
            <div class="grid">
                <div class="card">
                    <h3><i class="fas fa-mobile-alt"></i> Phase 2 Developments</h3>
                    <ul>
                        <li>Native mobile applications</li>
                        <li>RESTful API integration</li>
                        <li>Machine learning analytics</li>
                        <li>Blockchain record keeping</li>
                        <li>Multi-language support</li>
                    </ul>
                </div>
                <div class="card">
                    <h3><i class="fas fa-link"></i> Integration Opportunities</h3>
                    <ul>
                        <li>HRMIS system connection</li>
                        <li>Payment gateway integration</li>
                        <li>Video interviewing platform</li>
                        <li>Social media integration</li>
                        <li>Government portal connection</li>
                    </ul>
                </div>
            </div>

            <!-- Added Development Roadmap Chart -->
            <div class="chart-container">
                <canvas id="roadmapChart"></canvas>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <h3><i class="fas fa-star"></i> DERS - Leading Government Recruitment Innovation</h3>
            <p>Transforming traditional paper-based recruitment into streamlined digital workflows</p>
            <p><strong>Version:</strong> v1.0 | <strong>Framework:</strong> CodeIgniter 4 | <strong>Country:</strong> Papua New Guinea</p>
        </div>
    </div>

    <!-- JavaScript for Infographics -->
    <script>
        // Wait for DOM to be fully loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Animate numbers with CountUp.js
            const timeReduction = new CountUp('time-reduction', 0, 70, 0, 2.5, {suffix: '%'});
            const concurrentUsers = new CountUp('concurrent-users', 0, 1000, 0, 2.5, {suffix: '+'});
            const uptime = new CountUp('uptime', 0, 99.5, 1, 2.5, {suffix: '%'});
            const applications = new CountUp('applications', 0, 10, 0, 2.5, {suffix: 'K+'});
            
            timeReduction.start();
            concurrentUsers.start();
            uptime.start();
            applications.start();

            // Performance Metrics Chart
            const performanceCtx = document.getElementById('performanceChart').getContext('2d');
            const performanceChart = new Chart(performanceCtx, {
                type: 'bar',
                data: {
                    labels: ['Processing Time (days)', 'Manual Steps', 'Error Rate (%)', 'Transparency (%)', 'Compliance (%)'],
                    datasets: [{
                        label: 'Traditional Process',
                        data: [90, 45, 15, 40, 70],
                        backgroundColor: 'rgba(0, 0, 0, 0.7)',
                        borderColor: 'rgba(0, 0, 0, 1)',
                        borderWidth: 1
                    }, {
                        label: 'DERS System',
                        data: [30, 10, 3, 90, 85],
                        backgroundColor: 'rgba(230, 0, 0, 0.7)',
                        borderColor: 'rgba(230, 0, 0, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Performance Comparison: Traditional vs. DERS',
                            font: { size: 16 }
                        },
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Relative Score (lower is better for first 3)',
                                font: { size: 14 }
                            }
                        }
                    }
                }
            });

            // Technology Stack Donut Charts
            const backendCtx = document.getElementById('backendChart').getContext('2d');
            const backendChart = new Chart(backendCtx, {
                type: 'doughnut',
                data: {
                    labels: ['PHP', 'MySQL', 'APIs', 'Other'],
                    datasets: [{
                        data: [65, 20, 10, 5],
                        backgroundColor: [
                            'rgba(230, 0, 0, 0.8)',
                            'rgba(204, 0, 0, 0.8)',
                            'rgba(255, 193, 7, 0.8)',
                            'rgba(0, 0, 0, 0.8)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                font: { size: 10 }
                            }
                        }
                    },
                    cutout: '70%'
                }
            });

            const frontendCtx = document.getElementById('frontendChart').getContext('2d');
            const frontendChart = new Chart(frontendCtx, {
                type: 'doughnut',
                data: {
                    labels: ['HTML/CSS', 'JavaScript', 'Bootstrap', 'Other'],
                    datasets: [{
                        data: [40, 25, 30, 5],
                        backgroundColor: [
                            'rgba(230, 0, 0, 0.8)',
                            'rgba(204, 0, 0, 0.8)',
                            'rgba(255, 193, 7, 0.8)',
                            'rgba(0, 0, 0, 0.8)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                font: { size: 10 }
                            }
                        }
                    },
                    cutout: '70%'
                }
            });

            const infrastructureCtx = document.getElementById('infrastructureChart').getContext('2d');
            const infrastructureChart = new Chart(infrastructureCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Apache', 'Linux', 'Cloud', 'Security'],
                    datasets: [{
                        data: [30, 25, 25, 20],
                        backgroundColor: [
                            'rgba(230, 0, 0, 0.8)',
                            'rgba(204, 0, 0, 0.8)',
                            'rgba(255, 193, 7, 0.8)',
                            'rgba(0, 0, 0, 0.8)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                font: { size: 10 }
                            }
                        }
                    },
                    cutout: '70%'
                }
            });

            // Role Distribution Chart
            const roleDistributionCtx = document.getElementById('roleDistributionChart').getContext('2d');
            const roleDistributionChart = new Chart(roleDistributionCtx, {
                type: 'pie',
                data: {
                    labels: ['System Admins', 'Org Admins', 'HR Supervisors', 'HR Officers', 'Applicants'],
                    datasets: [{
                        data: [5, 15, 20, 60, 1000],
                        backgroundColor: [
                            'rgba(230, 0, 0, 0.8)',
                            'rgba(204, 0, 0, 0.8)',
                            'rgba(255, 193, 7, 0.8)',
                            'rgba(255, 214, 102, 0.8)',
                            'rgba(0, 0, 0, 0.8)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'User Role Distribution (not to scale)',
                            font: { size: 16 }
                        },
                        legend: {
                            position: 'right'
                        }
                    }
                }
            });

            // Feature Usage Radar Chart
            const featureUsageCtx = document.getElementById('featureUsageChart').getContext('2d');
            const featureUsageChart = new Chart(featureUsageCtx, {
                type: 'radar',
                data: {
                    labels: [
                        'Organization Management',
                        'Exercise Management',
                        'Position Management',
                        'Applicant Portal',
                        'Reporting Suite',
                        'Security & Compliance'
                    ],
                    datasets: [{
                        label: 'Feature Usage',
                        data: [85, 95, 90, 100, 80, 90],
                        backgroundColor: 'rgba(230, 0, 0, 0.2)',
                        borderColor: 'rgba(230, 0, 0, 1)',
                        pointBackgroundColor: 'rgba(230, 0, 0, 1)',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: 'rgba(230, 0, 0, 1)'
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Feature Usage & Importance',
                            font: { size: 16 }
                        }
                    },
                    scales: {
                        r: {
                            angleLines: {
                                display: true
                            },
                            suggestedMin: 50,
                            suggestedMax: 100
                        }
                    }
                }
            });

            // Benefits Comparison Chart
            const benefitsComparisonCtx = document.getElementById('benefitsComparisonChart').getContext('2d');
            const benefitsComparisonChart = new Chart(benefitsComparisonCtx, {
                type: 'bar',
                data: {
                    labels: ['Time Savings', 'Cost Reduction', 'Error Reduction', 'User Satisfaction', 'Compliance'],
                    datasets: [{
                        label: 'Organizations',
                        data: [80, 65, 75, 70, 90],
                        backgroundColor: 'rgba(230, 0, 0, 0.7)'
                    }, {
                        label: 'Applicants',
                        data: [60, 50, 80, 85, 75],
                        backgroundColor: 'rgba(255, 193, 7, 0.7)'
                    }, {
                        label: 'Government',
                        data: [70, 80, 85, 65, 95],
                        backgroundColor: 'rgba(0, 0, 0, 0.7)'
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Benefits by Stakeholder Group',
                            font: { size: 16 }
                        },
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: 'Benefit Score',
                                font: { size: 14 }
                            }
                        }
                    }
                }
            });

            // Development Roadmap Chart
            const roadmapCtx = document.getElementById('roadmapChart').getContext('2d');
            const roadmapChart = new Chart(roadmapCtx, {
                type: 'line',
                data: {
                    labels: ['Q1 2023', 'Q2 2023', 'Q3 2023', 'Q4 2023', 'Q1 2024', 'Q2 2024'],
                    datasets: [{
                        label: 'Core Features',
                        data: [100, 100, 100, 100, 100, 100],
                        borderColor: 'rgba(230, 0, 0, 1)',
                        backgroundColor: 'rgba(230, 0, 0, 0.2)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'Mobile Apps',
                        data: [0, 20, 40, 60, 80, 90],
                        borderColor: 'rgba(255, 193, 7, 1)',
                        backgroundColor: 'rgba(255, 193, 7, 0.2)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'API Integration',
                        data: [10, 30, 50, 70, 85, 95],
                        borderColor: 'rgba(0, 0, 0, 1)',
                        backgroundColor: 'rgba(0, 0, 0, 0.2)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'AI/ML Features',
                        data: [0, 0, 15, 30, 50, 70],
                        borderColor: 'rgba(204, 0, 0, 1)',
                        backgroundColor: 'rgba(204, 0, 0, 0.2)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Development Roadmap & Feature Completion',
                            font: { size: 16 }
                        },
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: 'Completion Percentage',
                                font: { size: 14 }
                            }
                        }
                    }
                }
            });

            // Animate progress bars on scroll
            const progressBars = document.querySelectorAll('.progress-bar');
            const animateProgressBars = () => {
                progressBars.forEach(bar => {
                    const rect = bar.getBoundingClientRect();
                    if (rect.top < window.innerHeight && rect.bottom > 0) {
                        const targetWidth = bar.getAttribute('data-width');
                        bar.style.width = targetWidth;
                    }
                });
            };

            // Initial check and scroll listener
            animateProgressBars();
            window.addEventListener('scroll', animateProgressBars);

            // D3.js visualization for workflow
            // This is a simple example - you can expand this for more complex visualizations
            d3.selectAll('.workflow-step')
                .on('mouseover', function() {
                    d3.select(this)
                        .transition()
                        .duration(300)
                        .style('transform', 'scale(1.05)');
                })
                .on('mouseout', function() {
                    d3.select(this)
                        .transition()
                        .duration(300)
                        .style('transform', 'scale(1)');
                });
        });
    </script>
</body>
</html>